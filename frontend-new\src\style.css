:root {
  /* Color Palette */
  --color-primary: #007bff; /* Professional Blue */
  --color-primary-dark: #0056b3; /* Darker Blue for hover */
  --color-secondary: #6c757d; /* Secondary Gray */
  --color-success: #28a745; /* Green for success */
  --color-warning: #ffc107; /* Yellow for warning */
  --color-danger: #dc3545; /* Red for danger/error */
  --color-info: #17a2b8; /* Cyan for informational */

  /* Neutral Palette */
  --color-white: #ffffff;
  --color-light-gray: #f8f9fa; /* Very light background */
  --color-mid-light-gray: #e9ecef; /* Borders, separators */
  --color-gray: #ced4da; /* Inputs, borders */
  --color-dark-gray: #6c757d; /* Secondary text */
  --color-very-dark-gray: #343a40; /* Body text */

  /* Background */
  --color-background: var(--color-light-gray);

  /* Typography */
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif; /* Using Inter as an example, fallback to system fonts */
  line-height: 1.6;
  font-weight: 400;
  color: var(--color-very-dark-gray); /* Default text color */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

   /* Surgery Type Colors */
  --color-surgery-cabg: #007bff; /* Blue - Cardiac */
  --color-surgery-knee: #28a745; /* Green - Orthopedic */
  --color-surgery-appen: #ffc107; /* Yellow - General */
  --color-surgery-herni: #6c757d; /* Gray - General */
  --color-surgery-catar: #17a2b8; /* Cyan - Ophthalmology */
  --color-surgery-hipre: #20c997; /* Teal - Orthopedic */

  --color-sdst-segment: rgba(255, 255, 255, 0.3); /* Semi-transparent overlay for SDST in blocks */
  --color-sdst-border: rgba(0, 0, 0, 0.2); /* Border around SDST segment */

  /* Additional colors for UI states */
  --color-background-soft: #f1f5f9; /* Slightly darker than background for panels */
  --color-background-mute: #e2e8f0; /* Even darker for active elements */
  --color-background-hover: rgba(0, 0, 0, 0.02); /* Subtle hover effect */
  --color-background-active: rgba(0, 0, 0, 0.05); /* Slightly stronger for active state */

  /* Text colors */
  --color-text: var(--color-very-dark-gray);
  --color-text-secondary: var(--color-dark-gray);
  --color-text-inverted: var(--color-white);

  /* Border colors */
  --color-border: var(--color-mid-light-gray);
  --color-border-soft: var(--color-light-gray);

  /* Accent color for highlights */
  --color-accent: #f59e0b; /* Amber for highlights and current time indicator */

  /* Error color with RGB components for alpha operations */
  --color-error: #dc3545;
  --color-error-rgb: 220, 53, 69;

  /* Primary color with RGB components for alpha operations */
  --color-primary-rgb: 0, 123, 255;

  /* Focus outline color */
  --color-focus: var(--color-primary);


  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Typography */
  --font-size-sm: 0.875rem; /* ~14px */
  --font-size-base: 1rem; /* ~16px */
  --font-size-lg: 1.125rem; /* ~18px */
  --font-size-xl: 1.25rem; /* ~20px */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;

  /* Z-index (Example) */
  --z-index-tooltip: 100;
  --z-index-modal: 1000;

  /* Layout (Example, replace with actual header/footer height if they exist in a layout component) */
  --header-height: 0px; /* Assuming no fixed header within this scope for now */
  --footer-height: 0px; /* Assuming no fixed footer within this scope for now */
}

/* Global box-sizing for easier layout calculations */
*, *::before, *::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh; /* Ensure body takes at least full viewport height */
  background-color: var(--color-background);
  color: var(--color-very-dark-gray);
}

#app {
  /* App container should not have default padding/margin if layout component handles it */
  max-width: none;
  margin: 0;
  padding: 0;
  text-align: left; /* Align content left by default */
  height: 100vh; /* Ensure app container takes full height */
}

h1, h2, h3, h4, h5, h6 {
  color: var(--color-very-dark-gray); /* Headings color */
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2.5em;
}

h2 {
  font-size: 2em;
}

h3 {
  font-size: 1.75em;
}

a {
  font-weight: 500;
  color: var(--color-primary);
  text-decoration: none; /* Remove default underline */
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline; /* Add underline on hover */
}

button {
  border-radius: 4px; /* Slightly less rounded */
  border: 1px solid transparent;
  padding: 0.8em 1.5em; /* More padding */
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--color-primary);
  color: var(--color-white);
  cursor: pointer;
  transition: background-color 0.25s ease, border-color 0.25s ease;
}

button:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark); /* Match border on hover */
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
  outline-color: var(--color-primary); /* Use primary color for focus */
}

input[type="text"],
input[type="password"],
input[type="email"],
textarea,
select {
  padding: 10px;
  border: 1px solid var(--color-mid-light-gray);
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1em;
  color: var(--color-very-dark-gray);
  background-color: var(--color-white);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Focus visible ring */
}

/* Add styles for focus indicator (WCAG 2.4.7) - override default if needed */
*:focus-visible {
    outline: 2px solid var(--color-accent); /* Use accent color for focus */
    outline-offset: 2px;
}

/* --- App Layout Styles (from previous step, add if not already present) --- */
/* Note: These styles assume #app is the container for the main layout */
/* If using a separate AppLayout component, apply these styles there */

/* Basic layout for the Surgery Scheduling Screen */
.surgery-scheduling-layout {
  display: flex;
  /* Use calc for height if there's a fixed header/footer */
  height: calc(100vh - var(--header-height) - var(--footer-height)); /* Example height calculation based on variables */
  overflow: hidden; /* Prevent outer scrollbars */
}

.main-schedule-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Manage scrolling within this area */
}

/* .pending-surgeries-panel styles are mostly in PendingSurgeriesList.vue */
/* .details-panel styles are mostly in SurgeryDetailsPanel.vue */

/* Add any other global styles or utility classes here */

/* Helper class for visually hiding elements for accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
