// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AppLayout > matches snapshot 1`] = `
"<div data-v-6f6437fc="" class="app-layout">
  <header data-v-6f6437fc="" class="top-nav-bar">
    <div data-v-6f6437fc="" class="app-brand"><button data-v-6f6437fc="" class="icon-button toggle-sidebar-button">
        <!-- Hamburger or arrow icon -->
        <!-- Right arrow --><span data-v-6f6437fc="">◄</span><!-- Left arrow -->
      </button><!-- App Logo/Name --><img data-v-6f6437fc="" src="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20role='img'%20class='iconify%20iconify--logos'%20width='31.88'%20height='32'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%20256%20257'%3e%3cdefs%3e%3clinearGradient%20id='IconifyId1813088fe1fbc01fb466'%20x1='-.828%25'%20x2='57.636%25'%20y1='7.652%25'%20y2='78.411%25'%3e%3cstop%20offset='0%25'%20stop-color='%2341D1FF'%3e%3c/stop%3e%3cstop%20offset='100%25'%20stop-color='%23BD34FE'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient%20id='IconifyId1813088fe1fbc01fb467'%20x1='43.376%25'%20x2='50.316%25'%20y1='2.242%25'%20y2='89.03%25'%3e%3cstop%20offset='0%25'%20stop-color='%23FFEA83'%3e%3c/stop%3e%3cstop%20offset='8.333%25'%20stop-color='%23FFDD35'%3e%3c/stop%3e%3cstop%20offset='100%25'%20stop-color='%23FFA800'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cpath%20fill='url(%23IconifyId1813088fe1fbc01fb466)'%20d='M255.153%2037.938L134.897%20252.976c-2.483%204.44-8.862%204.466-11.382.048L.875%2037.958c-2.746-4.814%201.371-10.646%206.827-9.67l120.385%2021.517a6.537%206.537%200%200%200%202.322-.004l117.867-21.483c5.438-.991%209.574%204.796%206.877%209.62Z'%3e%3c/path%3e%3cpath%20fill='url(%23IconifyId1813088fe1fbc01fb467)'%20d='M185.432.063L96.44%2017.501a3.268%203.268%200%200%200-2.634%203.014l-5.474%2092.456a3.268%203.268%200%200%200%203.997%203.378l24.777-5.718c2.318-.535%204.413%201.507%203.936%203.838l-7.361%2036.047c-.495%202.426%201.782%204.5%204.151%203.78l15.304-4.649c2.372-.72%204.652%201.36%204.15%203.788l-11.698%2056.621c-.732%203.542%203.979%205.473%205.943%202.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505%204.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z'%3e%3c/path%3e%3c/svg%3e" alt="App Logo" class="app-logo-small"><!-- Assuming vite.svg is in public folder --><span data-v-6f6437fc="">Surgery Scheduler</span></div>
    <div data-v-6f6437fc="" class="global-search">
      <!-- Global Search Bar --><input data-v-6f6437fc="" type="text" placeholder="Search...">
    </div>
    <div data-v-6f6437fc="" class="user-utilities">
      <!-- Notification Icon --><button data-v-6f6437fc="" class="icon-button">🔔</button><!-- User Profile Dropdown -->
      <div data-v-6f6437fc="" class="user-profile"><span data-v-6f6437fc="">User Name</span><!-- This should be dynamic later -->
        <!-- Dropdown icon/button here -->
      </div>
    </div>
  </header>
  <aside data-v-6f6437fc="" class="left-sidebar">
    <!-- Navigation Links -->
    <nav data-v-6f6437fc="">
      <ul data-v-6f6437fc="">
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/dashboard"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/scheduling"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/resource-management"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/sdst-data-management"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/reporting-analytics"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/notifications"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/administration"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/patient-management"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/my-profile-settings"></router-link-stub>
        </li>
        <li data-v-6f6437fc="">
          <router-link-stub data-v-6f6437fc="" to="/help-documentation"></router-link-stub>
        </li>
        <li data-v-6f6437fc="" class="logout-item"><button data-v-6f6437fc="" class="logout-button"><span data-v-6f6437fc="" class="nav-icon">🚪</span><span data-v-6f6437fc="" class="nav-text">Logout</span></button></li>
      </ul>
    </nav>
  </aside>
  <main data-v-6f6437fc="" class="main-content">
    <!-- Router View renders the specific page component -->
    <router-view-stub data-v-6f6437fc=""></router-view-stub>
  </main><!-- Toast notifications are typically triggered programmatically, not placed as a component here -->
  <!-- Toasts will be rendered by the plugin at the root level -->
</div>"
`;
